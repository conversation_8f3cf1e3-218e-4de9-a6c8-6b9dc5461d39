import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogOverlay, DialogPortal } from "@/components/ui/dialog";
import { cn } from "@/lib/utils";
import * as DialogPrimitive from "@radix-ui/react-dialog";
import { Command as CommandPrimitive } from "cmdk";
import { Variants, motion } from "framer-motion";
import {
    ArrowRight,
    Bell,
    Calendar,
    CalendarPlus,
    ChartBar,
    CheckCircle2,
    ClipboardList,
    Cloud,
    Command,
    Globe,
    HelpCircle,
    LayoutDashboard,
    Search,
    SearchX,
    Settings,
    Shield,
    UserPlus,
    Users,
    X,
    Zap,
} from "lucide-react";
import * as React from "react";
import { useCallback, useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";

// Custom Dialog Content without the default close button
const DialogContentWithoutCloseButton = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>
>(({ className, children, ...props }, ref) => (
  <DialogPortal>
    <DialogOverlay />
    <DialogPrimitive.Content
      ref={ref}
      className={cn(
        "bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",
        className,
      )}
      {...props}
    >
      {children}
    </DialogPrimitive.Content>
  </DialogPortal>
));
DialogContentWithoutCloseButton.displayName = "DialogContentWithoutCloseButton";

const fadeInUp = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.8 },
};

const fadeIn = {
  initial: { opacity: 0 },
  animate: { opacity: 1 },
  transition: { duration: 1.2 },
};

const stagger = {
  animate: {
    transition: {
      staggerChildren: 0.2,
    },
  },
};

const AppleIcon = ({ className }: { className?: string }) => (
  <svg viewBox="0 0 384 512" className={className} fill="currentColor">
    <path d="M318.7 268.7c-.2-36.7 16.4-64.4 50-84.8-18.8-26.9-47.2-41.7-84.7-44.6-35.5-2.8-74.3 20.7-88.5 20.7-15 0-49.4-19.7-76.4-19.7C63.3 141.2 4 184.8 4 273.5q0 39.3 14.4 81.2c12.8 36.7 59 126.7 107.2 125.2 25.2-.6 43-17.9 75.8-17.9 31.8 0 48.3 17.9 76.4 17.9 48.6-.7 90.4-82.5 102.6-119.3-65.2-30.7-61.7-90-61.7-91.9zm-56.6-164.2c27.3-32.4 24.8-61.9 24-72.5-24.1 1.4-52 16.4-67.9 34.9-17.5 19.8-27.8 44.3-25.6 71.9 26.1 2 49.9-11.4 69.5-34.3z" />
  </svg>
);

const AndroidIcon = ({ className }: { className?: string }) => (
  <svg viewBox="0 0 24 24" className={className} fill="currentColor">
    <path d="M17.523 15.3414c-.5511 0-.9993-.4486-.9993-.9997s.4482-.9993.9993-.9993c.5511 0 .9993.4482.9993.9993s-.4482.9997-.9993.9997m-11.046 0c-.5511 0-.9993-.4486-.9993-.9997s.4482-.9993.9993-.9993c.5511 0 .9993.4482.9993.9993s-.4482.9997-.9993.9997m11.4045-6.02l1.9973-3.4592a.416.416 0 00-.1521-.5676.416.416 0 00-.5676.1521l-2.0223 3.503C15.5902 8.4094 13.8533 8 12 8s-3.5902.4094-5.1367.9597L4.841 5.4565a.4161.4161 0 00-.5677-.1521.4157.4157 0 00-.1521.5676l1.9973 3.4592C2.6889 11.1867.3432 14.6589 0 18.75h24c-.3432-4.0911-2.6889-7.5633-6.1185-9.4286" />
  </svg>
);

const platforms = [
  {
    icon: Globe,
    text: "Web App",
    description: "Access from any browser",
  },
  {
    icon: AppleIcon,
    text: "iOS App",
    description: "Native iPhone & iPad app",
  },
  {
    icon: AndroidIcon,
    text: "Android App",
    description: "Native Android experience",
  },
];

const features = [
  {
    icon: Shield,
    text: "HIPAA Compliant",
    description: "Enterprise-grade security",
  },
  {
    icon: CheckCircle2,
    text: "ONC-ACB Certified",
    description: "Meets federal standards",
  },
  {
    icon: Cloud,
    text: "Real-time Sync",
    description: "Instant updates across devices",
  },
  { icon: Zap, text: "Fast & Reliable", description: "99.9% uptime guarantee" },
];

const mockupFeatures = [
  {
    title: "Patient Management",
    icon: Users,
    stats: "2.5k+ Active Patients",
    color: "from-blue-500/20 to-blue-500/5",
    gradient: "hover:from-blue-500/30 hover:to-blue-500/10",
  },
  {
    title: "Appointments",
    icon: Calendar,
    stats: "150+ Daily Sessions",
    color: "from-emerald-500/20 to-emerald-500/5",
    gradient: "hover:from-emerald-500/30 hover:to-emerald-500/10",
  },
  {
    title: "Clinical Notes",
    icon: ClipboardList,
    stats: "98% Completion Rate",
    color: "from-purple-500/20 to-purple-500/5",
    gradient: "hover:from-purple-500/30 hover:to-purple-500/10",
  },
  {
    title: "Analytics",
    icon: ChartBar,
    stats: "Real-time Insights",
    color: "from-orange-500/20 to-orange-500/5",
    gradient: "hover:from-orange-500/30 hover:to-orange-500/10",
  },
];

const pulseAnimation: Variants = {
  initial: { opacity: 0.5 },
  animate: {
    opacity: 1,
    transition: {
      duration: 2,
      repeat: Infinity,
      repeatType: "reverse" as const,
    },
  },
};

const slideUpStagger: Variants = {
  initial: { opacity: 0, y: 20 },
  animate: (i: number) => ({
    opacity: 1,
    y: 0,
    transition: {
      delay: i * 0.1,
      duration: 0.5,
      ease: "easeOut",
    },
  }),
};

const shortcuts = [
  { keys: "⌘ K", description: "Quick search" },
  { keys: "⌘ J", description: "New patient" },
  { keys: "⌘ /", description: "Show shortcuts" },
];

const notifications = [
  "New appointment request",
  "Lab results ready",
  "Prescription renewal",
];

const searchCommands = [
  {
    id: "patients",
    name: "Search Patients",
    shortcut: ["p"],
    action: () => console.log("Search patients"),
    icon: Users,
    description: "Find and manage patient records",
  },
  {
    id: "appointments",
    name: "View Appointments",
    shortcut: ["a"],
    action: () => console.log("View appointments"),
    icon: Calendar,
    description: "Schedule and manage appointments",
  },
  {
    id: "notes",
    name: "Clinical Notes",
    shortcut: ["n"],
    action: () => console.log("Clinical notes"),
    icon: ClipboardList,
    description: "Create and edit clinical documentation",
  },
  {
    id: "analytics",
    name: "Analytics Dashboard",
    shortcut: ["d"],
    action: () => console.log("Analytics dashboard"),
    icon: ChartBar,
    description: "View practice performance metrics",
  },
];

const quickActions = [
  {
    id: "new-patient",
    name: "New Patient",
    shortcut: ["j"],
    action: () => console.log("Create new patient"),
    icon: UserPlus,
    description: "Register a new patient",
  },
  {
    id: "new-appointment",
    name: "New Appointment",
    shortcut: ["b"],
    action: () => console.log("Create new appointment"),
    icon: CalendarPlus,
    description: "Schedule a new appointment",
  },
];

const navigateCommands = [
  {
    id: "dashboard",
    name: "Go to Dashboard",
    shortcut: ["g", "d"],
    action: () => console.log("Navigate to dashboard"),
    icon: LayoutDashboard,
    description: "View your main dashboard",
  },
  {
    id: "settings",
    name: "Settings",
    shortcut: ["g", "s"],
    action: () => console.log("Navigate to settings"),
    icon: Settings,
    description: "Configure application settings",
  },
  {
    id: "help",
    name: "Help & Support",
    shortcut: ["g", "h"],
    action: () => console.log("Navigate to help"),
    icon: HelpCircle,
    description: "Get help and support",
  },
];

export function Hero() {
  const navigate = useNavigate();
  const [scrollY, setScrollY] = useState(0);
  const [isCommandOpen, setIsCommandOpen] = useState(false);
  const [isMac, setIsMac] = useState(false);

  useEffect(() => {
    const handleScroll = () => setScrollY(window.scrollY);
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  useEffect(() => {
    // Detect OS
    setIsMac(navigator.platform.toLowerCase().includes("mac"));
  }, []);

  const commandKey = isMac ? "⌘" : "Ctrl";

  const handleKeyDown = useCallback(
    (e: KeyboardEvent) => {
      // Check for Command/Ctrl + K
      if ((isMac ? e.metaKey : e.ctrlKey) && e.key === "k") {
        e.preventDefault();
        setIsCommandOpen(true);
      }
    },
    [isMac],
  );

  useEffect(() => {
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [handleKeyDown]);

  return (
    <>
      <section
        className="relative min-h-screen flex items-center justify-center pt-16 overflow-hidden bg-gradient-to-b from-background to-background/95"
        role="region"
        aria-label="Hero section"
      >
        <div className="absolute inset-0 bg-grid-white/[0.02] bg-[size:75px_75px]" />

        {/* Animated Background Gradients */}
        <div className="absolute inset-0 flex items-center justify-center overflow-hidden">
          <motion.div
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.2, 0.3, 0.2],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut",
              times: [0, 0.5, 1],
            }}
            className="absolute inset-auto w-[600px] h-[600px] bg-gradient-to-tr from-emerald-500/30 to-blue-600/30 rounded-full blur-3xl"
          />
          <motion.div
            animate={{
              scale: [1.2, 1, 1.2],
              opacity: [0.2, 0.3, 0.2],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut",
              times: [0, 0.5, 1],
            }}
            className="absolute inset-auto w-[500px] h-[500px] bg-gradient-to-bl from-blue-600/30 to-emerald-500/30 rounded-full blur-3xl"
          />
          <motion.div
            animate={{
              scale: [1.1, 1.3, 1.1],
              opacity: [0.15, 0.25, 0.15],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut",
              times: [0, 0.5, 1],
            }}
            className="absolute inset-auto w-[400px] h-[400px] bg-gradient-to-tl from-emerald-500/20 to-blue-600/20 rounded-full blur-3xl"
          />
        </div>

        {/* Particle Effect */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {Array.from({ length: 20 }).map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-primary/20 rounded-full"
              animate={{
                x: [
                  Math.random() * window.innerWidth,
                  Math.random() * window.innerWidth,
                ],
                y: [
                  Math.random() * window.innerHeight,
                  Math.random() * window.innerHeight,
                ],
                opacity: [0.2, 0.8, 0.2],
                scale: [1, 1.5, 1],
              }}
              transition={{
                duration: Math.random() * 10 + 10,
                repeat: Infinity,
                ease: "linear",
              }}
              style={{
                left: Math.random() * 100 + "%",
                top: Math.random() * 100 + "%",
              }}
            />
          ))}
        </div>

        <div className="container relative z-10 mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left Column */}
            <motion.div
              initial="initial"
              animate="animate"
              variants={stagger}
              className="max-w-2xl"
            >
              <motion.div
                variants={fadeInUp}
                className="flex flex-wrap gap-2 mb-6"
              >
                {platforms.map((platform, index) => (
                  <Badge
                    key={index}
                    variant="outline"
                    className="py-2 px-4 text-sm bg-primary/5 border-primary/20 flex items-center gap-2 group hover:bg-primary/10 hover:border-emerald-500/40 hover:shadow-[0_0_10px_rgba(16,185,129,0.15)] transition-all duration-300"
                  >
                    <platform.icon className="w-6 h-6 shrink-0" />
                    <div>
                      <div className="font-medium relative">
                        <span className="relative z-10 transition-opacity duration-300 group-hover:opacity-0">
                          {platform.text}
                        </span>
                        <span className="absolute inset-0 bg-gradient-to-r from-emerald-500 to-blue-600 bg-clip-text text-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                          {platform.text}
                        </span>
                      </div>
                      <div className="text-xs text-muted-foreground relative">
                        <span className="relative z-10 transition-opacity duration-300 group-hover:opacity-0">
                          {platform.description}
                        </span>
                        <span className="absolute inset-0 bg-gradient-to-r from-emerald-500 to-blue-600 bg-clip-text text-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                          {platform.description}
                        </span>
                      </div>
                    </div>
                  </Badge>
                ))}
              </motion.div>

              <motion.h1
                variants={fadeInUp}
                className="text-5xl sm:text-6xl lg:text-7xl font-bold mb-6 bg-gradient-to-r from-emerald-500 to-blue-600 bg-clip-text text-transparent"
              >
                Enterprise EHR Platform for Modern Healthcare
              </motion.h1>

              <motion.p
                variants={fadeInUp}
                className="text-xl mb-8 text-muted-foreground leading-relaxed"
              >
                A comprehensive healthcare platform that unifies web and mobile
                experiences. Built for performance, security, and seamless
                cross-device synchronization.
              </motion.p>

              <motion.div
                variants={fadeInUp}
                className="grid grid-cols-2 gap-4 mb-8"
              >
                {features.map((feature, index) => (
                  <div
                    key={index}
                    className="flex items-start gap-3 p-3 rounded-lg bg-primary/5 border border-primary/10 group hover:border-emerald-500/40 hover:shadow-[0_0_10px_rgba(16,185,129,0.15)] transition-all duration-300"
                  >
                    <feature.icon className="w-5 h-5 text-primary mt-0.5" />
                    <div>
                      <div className="font-medium relative">
                        <span className="relative z-10 transition-opacity duration-300 group-hover:opacity-0">
                          {feature.text}
                        </span>
                        <span className="absolute inset-0 bg-gradient-to-r from-emerald-500 to-blue-600 bg-clip-text text-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                          {feature.text}
                        </span>
                      </div>
                      <div className="text-sm text-muted-foreground relative">
                        <span className="relative z-10 transition-opacity duration-300 group-hover:opacity-0">
                          {feature.description}
                        </span>
                        <span className="absolute inset-0 bg-gradient-to-r from-emerald-500 to-blue-600 bg-clip-text text-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                          {feature.description}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </motion.div>

              <motion.div
                variants={fadeInUp}
                className="flex flex-col sm:flex-row gap-4"
              >
                <Button
                  size="lg"
                  className="text-lg px-8 py-6 bg-gradient-to-r from-emerald-500 to-blue-600 text-black font-medium hover:opacity-90 shadow-lg hover:shadow-xl transition-all duration-300 group"
                  onClick={() => navigate("/register")}
                >
                  Start Free Trial
                  <ArrowRight className="ml-2 w-5 h-5 transition-transform group-hover:translate-x-1 text-black" />
                </Button>
                <Button
                  size="lg"
                  variant="outline"
                  className="text-lg px-8 py-6 border-primary/20 hover:bg-primary/5 shadow-lg hover:shadow-xl transition-all duration-300 group overflow-hidden"
                  onClick={() => navigate("/demo")}
                >
                  <span className="relative inline-block">
                    <span className="relative z-10 transition-opacity duration-300 group-hover:opacity-0">
                      Watch Demo
                    </span>
                    <span className="absolute inset-0 bg-gradient-to-r from-emerald-500 to-blue-600 bg-clip-text text-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      Watch Demo
                    </span>
                  </span>
                </Button>
              </motion.div>
            </motion.div>

            {/* Right Column */}
            <motion.div
              variants={fadeIn}
              initial="initial"
              animate="animate"
              className="relative hidden lg:block"
              style={{
                transform: `translateY(${scrollY * 0.1}px)`,
              }}
            >
              <div className="relative grid grid-cols-2 gap-4">
                {mockupFeatures.map((feature, index) => (
                  <motion.div
                    key={index}
                    custom={index}
                    variants={slideUpStagger}
                    whileHover={{ scale: 1.02 }}
                    className={`relative overflow-hidden rounded-xl border border-primary/10 shadow-lg transition-colors duration-300 cursor-pointer group`}
                    role="button"
                    aria-label={`${feature.title} feature card showing ${feature.stats}`}
                  >
                    <div
                      className={`absolute inset-0 bg-gradient-to-br ${feature.color} transition-colors duration-300 ${feature.gradient}`}
                    />
                    <div className="relative p-4">
                      <div className="flex items-center gap-3 mb-3">
                        <motion.div
                          whileHover={{ rotate: 15 }}
                          className="p-2 rounded-lg bg-gradient-to-r from-emerald-500/20 to-blue-600/20 shadow-sm transition-transform duration-300 group-hover:scale-110 group-hover:from-emerald-500/30 group-hover:to-blue-600/30"
                        >
                          <feature.icon className="w-5 h-5 text-primary" />
                        </motion.div>
                        <div>
                          <div className="font-medium text-sm">
                            {feature.title}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {feature.stats}
                          </div>
                        </div>
                      </div>
                      <motion.div
                        variants={pulseAnimation}
                        className="space-y-2"
                      >
                        <div className="h-2 bg-primary/10 rounded-full w-full" />
                        <div className="h-2 bg-primary/10 rounded-full w-2/3" />
                        <div className="h-2 bg-primary/10 rounded-full w-1/2" />
                      </motion.div>
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* Interface Preview */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4, duration: 0.5 }}
                className="mt-4 rounded-xl overflow-hidden border border-primary/10 shadow-lg bg-background/95 backdrop-blur-sm p-4"
              >
                {/* Command Bar */}
                <motion.div
                  className="flex items-center gap-3 mb-4 p-2 rounded-lg bg-primary/5 cursor-pointer hover:bg-primary/10 transition-colors"
                  whileHover={{ scale: 1.01 }}
                  onClick={() => setIsCommandOpen(true)}
                >
                  <Search className="w-4 h-4 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">
                    Quick search...
                  </span>
                  <div className="ml-auto flex items-center gap-2">
                    <kbd className="px-2 py-1 text-xs rounded bg-background border">
                      {commandKey} K
                    </kbd>
                  </div>
                {/* Top Bar */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <motion.div
                      whileHover={{ scale: 1.2 }}
                      className="w-2 h-2 rounded-full bg-red-500"
                    />
                    <motion.div
                      whileHover={{ scale: 1.2 }}
                      className="w-2 h-2 rounded-full bg-yellow-500"
                    />
                    <motion.div
                      whileHover={{ scale: 1.2 }}
                      className="w-2 h-2 rounded-full bg-green-500"
                    />
                  </div>
                  <div className="flex items-center gap-3">
                    <motion.div
                      whileHover={{ scale: 1.1 }}
                      className="relative"
                    >
                      <Bell className="w-4 h-4 text-primary" />
                      <span className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full" />
                    </motion.div>
                    <motion.div
                      whileHover={{ rotate: 180 }}
                      className="p-1 rounded-md bg-primary/10 transition-transform duration-300"
                    >
                      <Settings className="w-3 h-3 text-primary" />
                    </motion.div>
                  </div>
                </div>

                {/* Content Area */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <motion.div
                      variants={pulseAnimation}
                      className="h-8 bg-primary/5 rounded-lg w-48"
                    />
                    <div className="flex gap-2">
                      {shortcuts.map((shortcut, i) => (
                        <motion.div
                          key={i}
                          whileHover={{ y: -2 }}
                          className="flex items-center gap-2 px-2 py-1 rounded-md bg-primary/5 text-xs"
                        >
                          <kbd className="px-1.5 py-0.5 rounded bg-background border text-muted-foreground">
                            {shortcut.keys}
                          </kbd>
                          <span className="text-muted-foreground">
                            {shortcut.description}
                          </span>
                        </motion.div>
                      ))}
                    </div>
                  </div>

                  {/* Feature Grid */}
                  <div className="grid grid-cols-3 gap-2">
                    {[0, 1, 2].map((i) => (
                      <motion.div
                        key={i}
                        custom={i}
                        variants={slideUpStagger}
                        whileHover={{ scale: 1.02 }}
                        className="relative h-20 bg-primary/5 rounded-lg hover:bg-primary/10 transition-colors duration-300 cursor-pointer p-3 overflow-hidden group"
                      >
                        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                        <div className="relative z-10">
                          <div className="flex items-center gap-2 mb-2">
                            <Command className="w-3 h-3 text-primary" />
                            <span className="text-xs font-medium">
                              Quick Action {i + 1}
                            </span>
                          </div>
                          <div className="text-xs text-muted-foreground truncate">
                            {notifications[i]}
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </div>
              </motion.div>
            </motion.div>
          </div>
        </div>
      </section>

      <Dialog open={isCommandOpen} onOpenChange={setIsCommandOpen}>
        <DialogContentWithoutCloseButton className="p-0 gap-0 max-w-[640px] w-full rounded-xl">
          <CommandPrimitive className="[&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground [&_[cmdk-group]:not([hidden])_~[cmdk-group]]:pt-0 [&_[cmdk-group]]:px-2 [&_[cmdk-input-wrapper]_svg]:h-5 [&_[cmdk-input-wrapper]_svg]:w-5 [&_[cmdk-input]]:h-14 [&_[cmdk-item]]:px-2 [&_[cmdk-item]]:py-3 [&_[cmdk-item]_svg]:h-5 [&_[cmdk-item]_svg]:w-5 rounded-xl">
            <div
              className="flex items-center border-b px-4 py-2"
              cmdk-input-wrapper=""
            >
              <Search className="mr-2 h-5 w-5 shrink-0 text-primary" />
              <CommandPrimitive.Input
                className="flex h-11 w-full rounded-md bg-transparent py-3 text-base outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
                placeholder="Type a command or search..."
                autoFocus
              />
              <div className="flex items-center gap-2">
                <kbd className="hidden xs:flex h-5 select-none items-center gap-1 rounded border bg-background px-1.5 font-mono text-[10px] font-medium opacity-100 sm:text-xs">
                  <span className="text-xs">{commandKey}</span>K
                </kbd>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-7 w-7 rounded-full hover:bg-primary/10"
                  onClick={() => setIsCommandOpen(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
            <CommandPrimitive.List className="max-h-[400px] overflow-y-auto overflow-x-hidden p-2">
              <CommandPrimitive.Empty className="py-6 text-center text-sm">
                <div className="flex flex-col items-center justify-center gap-1">
                  <SearchX className="h-10 w-10 text-muted-foreground/50" />
                  <p className="text-muted-foreground">No results found.</p>
                  <p className="text-xs text-muted-foreground/70">
                    Try a different search term
                  </p>
                </div>
              </CommandPrimitive.Empty>

              <CommandPrimitive.Group heading="Quick Actions" className="pb-2">
                {quickActions.map((command) => (
                  <CommandPrimitive.Item
                    key={command.id}
                    onSelect={() => {
                      command.action();
                      setIsCommandOpen(false);
                    }}
                    className="group relative flex cursor-pointer select-none items-center rounded-lg px-3 py-2.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 transition-colors"
                  >
                    <div className="flex h-7 w-7 items-center justify-center rounded-full bg-primary/10 mr-3 group-hover:bg-primary/20 transition-colors">
                      <command.icon className="h-4 w-4 text-primary" />
                    </div>
                    <div className="flex flex-col flex-1">
                      <span className="font-medium">{command.name}</span>
                      <span className="text-xs text-muted-foreground">
                        {command.description}
                      </span>
                    </div>
                    <div className="flex items-center gap-1">
                      <kbd className="flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium opacity-100 sm:text-xs">
                        {commandKey} + {command.shortcut}
                      </kbd>
                    </div>
                  </CommandPrimitive.Item>
                ))}
              </CommandPrimitive.Group>

              <CommandPrimitive.Group heading="Main Features" className="py-2">
                {searchCommands.map((command) => (
                  <CommandPrimitive.Item
                    key={command.id}
                    onSelect={() => {
                      command.action();
                      setIsCommandOpen(false);
                    }}
                    className="group relative flex cursor-pointer select-none items-center rounded-lg px-3 py-2.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 transition-colors"
                  >
                    <div className="flex h-7 w-7 items-center justify-center rounded-full bg-primary/10 mr-3 group-hover:bg-primary/20 transition-colors">
                      <command.icon className="h-4 w-4 text-primary" />
                    </div>
                    <div className="flex flex-col flex-1">
                      <span className="font-medium">{command.name}</span>
                      <span className="text-xs text-muted-foreground">
                        {command.description}
                      </span>
                    </div>
                    <div className="flex items-center gap-1">
                      <kbd className="flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium opacity-100 sm:text-xs">
                        {commandKey} + {command.shortcut}
                      </kbd>
                    </div>
                  </CommandPrimitive.Item>
                ))}
              </CommandPrimitive.Group>

              <CommandPrimitive.Group heading="Navigation" className="py-2">
                {navigateCommands.map((command) => (
                  <CommandPrimitive.Item
                    key={command.id}
                    onSelect={() => {
                      command.action();
                      setIsCommandOpen(false);
                    }}
                    className="group relative flex cursor-pointer select-none items-center rounded-lg px-3 py-2.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 transition-colors"
                  >
                    <div className="flex h-7 w-7 items-center justify-center rounded-full bg-primary/10 mr-3 group-hover:bg-primary/20 transition-colors">
                      <command.icon className="h-4 w-4 text-primary" />
                    </div>
                    <div className="flex flex-col flex-1">
                      <span className="font-medium">{command.name}</span>
                      <span className="text-xs text-muted-foreground">
                        {command.description}
                      </span>
                    </div>
                    <div className="flex items-center gap-1">
                      {command.shortcut.map((key, i) => (
                        <kbd
                          key={i}
                          className="flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium opacity-100 sm:text-xs"
                        >
                          {i === 0 ? `${commandKey} + ${key}` : key}
                        </kbd>
                      ))}
                    </div>
                  </CommandPrimitive.Item>
                ))}
              </CommandPrimitive.Group>

              <div className="py-2 px-2 text-xs text-muted-foreground border-t mt-2">
                <div className="flex items-center justify-center">
                  <span>
                    Pro tip: Press {commandKey} + . to see all keyboard
                    shortcuts
                  </span>
                </div>
              </div>
            </CommandPrimitive.List>
          </CommandPrimitive>
        </DialogContentWithoutCloseButton>
      </Dialog>
    </>
  );
}
